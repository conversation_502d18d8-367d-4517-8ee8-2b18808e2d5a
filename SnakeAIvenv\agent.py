import torch
import random
import numpy as np
from collections import deque
from game import SnakeGameAI, Direction, Point, NUM_FOOD
from model import Linear_QNet, QTrainer
from helper import plot

MAX_MEMORY = 100000
BATCH_SIZE = 512
LR = 0.0005

class Agent:
    def __init__(self):
        self.n_games = 0
        self.epsilon = 0 # Random behavior
        self.gamma = 0.95 # Discount rate
        self.memory = deque(maxlen=MAX_MEMORY) # popleft() if max reached
        self.model = Linear_QNet(19, [256, 256], 3)
        self.trainer = QTrainer(self.model, lr=LR, gamma=self.gamma)
        self.move_history = deque(maxlen = 3)

    
    def get_state(self, game):
        head = game.snake[0]
        snake_len = len(game.snake)

        point_l = Point(head.x - 20, head.y)
        point_r = Point(head.x + 20, head.y)
        point_u = Point(head.x, head.y - 20)
        point_d = Point(head.x, head.y + 20)

        dir_l = game.direction == Direction.LEFT
        dir_r = game.direction == Direction.RIGHT
        dir_u = game.direction == Direction.UP
        dir_d = game.direction == Direction.DOWN

        dist_l = head.x / game.w
        dist_r = (game.w - head.x) / game.w
        dist_u = head.y / game.h
        dist_d = (game.h - head.y) / game.h

        # food_distances = [np.sqrt((head.x - food.x) ** 2 + (head.y - food.y) ** 2) for food in game.food if food is not None]
        food_distance = np.sqrt((head.x - game.food.x) ** 2 + (head.y - game.food.y) ** 2) if game.food is not None else 0

        state_part = [
            snake_len,
            # danger straight
            (dir_r and game.is_collision(point_r)) or
            (dir_l and game.is_collision(point_l)) or
            (dir_u and game.is_collision(point_u)) or
            (dir_d and game.is_collision(point_d)),

            # danger right
            (dir_u and game.is_collision(point_r)) or
            (dir_d and game.is_collision(point_l)) or
            (dir_l and game.is_collision(point_u)) or
            (dir_r and game.is_collision(point_d)),

            # danger left
            (dir_d and game.is_collision(point_r)) or
            (dir_u and game.is_collision(point_l)) or
            (dir_r and game.is_collision(point_u)) or
            (dir_l and game.is_collision(point_d)),

            # move direction
            dir_l,
            dir_r,
            dir_u,
            dir_d,

            dist_l,
            dist_r,
            dist_u,
            dist_d,

            # food location
            # any(game.food[i].x < game.head.x for i in range(NUM_FOOD)), 
            game.food.x < game.head.x,
            # food left
            # any(game.food[i].x > game.head.x for i in range(NUM_FOOD)), 
            game.food.x > game.head.x, 
            # food right
            # any(game.food[i].y < game.head.y for i in range(NUM_FOOD)), 
            game.food.y < game.head.y,
            # # food up
            # any(game.food[i].y > game.head.y for i in range(NUM_FOOD)),
            game.food.y > game.head.y, 
            # # food down
            food_distance,

            
        ]

        history_state = []
        for i in range(len(self.move_history)):
            history_state.extend(self.move_history[-(i+1)])

        state = state_part + history_state

        return np.array(state, dtype=int)

    def remember(self, state, action, reward, next_state, done):
        self.memory.append((state, action, reward, next_state, done))
    
    def train_long_memory(self):
        if len(self.memory) > BATCH_SIZE:
            mini_sample = random.sample(self.memory, BATCH_SIZE) # list of tuples
        else:
            mini_sample = self.memory

        states, actions, rewards, next_states, dones = zip(*mini_sample)
        self.trainer.train_step(states, actions, rewards, next_states, dones)
        # for state, action, reward, next_state, done in mini_sample:
        #     self.trainer.train_step(state, action, reward, next_state, done)
    
    def train_short_memory(self, state, action, reward, next_state, done):
        self.trainer.train_step(state, action, reward, next_state, done)
    
    def get_action(self, state):
        # random moves: tradeoff between exploration / exploitation
        # epsilon-greedy algorithm
        self.epsilon = 100 * (0.995 ** self.n_games) # Exponential decay
        # self.epsilon = max(1, 80 - self.n_games * 0.8) # Piecewise linear decay
        # self.epsilon = max(5, 100 - self.n_games) # Fixed minimum
        final_move = [0, 0, 0]
        if random.randint(0, 200) < self.epsilon:
            move = random.randint(0, 2)
            final_move[move] = 1
        else:
            state0 = torch.tensor(state, dtype=torch.float).unsqueeze(0)
            with torch.no_grad():
                prediction = self.model(state0)
            move = torch.argmax(prediction, dim=1).item()
            final_move[move] = 1
          
        return final_move
    
    
def train():
    plot_scores = []
    plot_mean_scores = []
    total_score = 0
    record = 0
    agent = Agent()
    game = SnakeGameAI()

    while True:
        # get old state
        state_old = agent.get_state(game)

        # get move
        final_move = agent.get_action(state_old)

        # perform move and get new state
        agent.move_history.append(final_move)
        reward, done, score = game.play_step(final_move)
        state_new = agent.get_state(game)

        # train short memory
        agent.train_short_memory(state_old, final_move, reward, state_new, done)

        # remember
        agent.remember(state_old, final_move, reward, state_new, done)

        if done:
            # train long memory, plot result
            game.reset()
            agent.n_games += 1
            agent.train_long_memory()

            if score > record:
                record = score
                agent.model.save()

            print('Game', agent.n_games, 'Score', score, 'Record:', record)

            plot_scores.append(score)
            total_score += score
            mean_score = total_score / agent.n_games
            plot_mean_scores.append(mean_score)
            # plot(plot_scores, plot_mean_scores)


if __name__ == '__main__':
    train()